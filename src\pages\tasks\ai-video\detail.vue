<script lang="ts" setup>
import TnNavbar from '@tuniao/tnui-vue3-uniapp/components/navbar/src/navbar.vue'
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnLineProgress from '@tuniao/tnui-vue3-uniapp/components/line-progress/src/line-progress.vue'
import { ref, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
import { useTaskPolling } from '@/hooks/useTaskPolling'
import { useTaskDelete } from '@/hooks/useTaskDelete'
import type { TaskDetail, RunwayTask, LumaTask } from './types'

// 定义Heihe2视频项类型
interface Heihe2VideoItem {
  index: number
  originalUrl: string
  uploadedUrl: string
}

const vk = uni.vk

// 定义任务类型枚举
enum TaskType {
  RUNWAY = 'runway',
  LUMA = 'luma',
  ZHIPU = 'zhipu',
  WANX = 'wanx',
  KELING = 'keling',
  MIDJOURNEY = 'midjourney',
  HEIHE2 = 'midjourney' // heihe2实际上使用midjourney模型
}

// 任务详情数据
const taskDetail = ref<TaskDetail | null>(null)

// 判断任务模型
const taskModel = computed(() => {
  return taskDetail.value?.model || TaskType.RUNWAY
})

// 是否是 Luma 任务
const isLumaTask = computed(() => {
  return taskModel.value === TaskType.LUMA
})

// 是否是智普任务
const isZhipuTask = computed(() => {
  return taskModel.value === TaskType.ZHIPU
})

// 是否是万象任务
const isWanxTask = computed(() => {
  return taskModel.value === TaskType.WANX
})

// 是否是可灵任务
const isKelingTask = computed(() => {
  return taskModel.value === TaskType.KELING
})

// 是否是heihe2任务
const isHeihe2Task = computed(() => {
  return taskModel.value === TaskType.MIDJOURNEY
})

// 获取参数显示配置
const getParamsConfig = computed(() => {
  if (isLumaTask.value) {
    return [
      {
        label: '比例',
        value: taskDetail.value?.params?.ratio || ''
      },
    ]
  }

  // 智普任务参数
  if (isZhipuTask.value) {
    return [
      {
        label: '比例',
        value: taskDetail.value?.params?.ratio || ''
      },
      {
        label: '时长',
        value: `${taskDetail.value?.params?.duration || 5}秒`
      },
      {
        label: '帧率',
        value: `${taskDetail.value?.params?.fps || 30}fps`
      },
      {
        label: '质量',
        value: taskDetail.value?.params?.quality === 'quality' ? '质量优先' : '速度优先'
      }
    ]
  }

  // 万象任务参数
  if (isWanxTask.value) {
    return [
      {
        label: '比例',
        value: taskDetail.value?.params?.ratio || ''
      },
      {
        label: '时长',
        value: `${taskDetail.value?.params?.duration || 5}秒`
      },

    ]
  }

  // Heihe2任务参数
  if (isHeihe2Task.value) {
    return [
      {
        label: '运动强度',
        value: taskDetail.value?.params?.motion === 'high' ? '高强度' : '低强度'
      },
      {
        label: '视频数量',
        value: `${taskDetail.value?.params?.videoCount || 4}个`
      }
    ]
  }

  // Runway 任务参数
  return [
    {
      label: '比例',
      value: taskDetail.value?.params?.ratio || ''
    },
    {
      label: '时长',
      value: `${taskDetail.value?.params?.options?.seconds || 5}秒`
    }
  ]
})

// 添加假进度相关的状态
const fakeProgress = ref(0)
const progressTimer = ref<number | null>(null)

// 启动假进度
const startFakeProgress = () => {
  // 清除可能存在的旧定时器
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
  }

  // 初始进度
  fakeProgress.value = 5

  // 设置定时器，每15秒更新一次进度 两个刷新时间不一样
  progressTimer.value = setInterval(() => {
    if (fakeProgress.value < 98) {
      // 根据当前进度动态计算增量
      const increment = fakeProgress.value < 50 ? 15 : 8
      fakeProgress.value = Math.min(98, fakeProgress.value + increment)
    }
  }, isLumaTask.value ? 5000 : 15000) as unknown as number
}

// 停止假进度
const stopFakeProgress = () => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = null
  }
  fakeProgress.value = 0
}

// 使用轮询 hook
const { startPolling, stopPolling } = useTaskPolling({
  interval: 5000,
  onSuccess: (data) => {
    if (data[0]) {
      const oldStatus = taskDetail.value?.status
      const newStatus = data[0].status

      taskDetail.value = {
        ...taskDetail.value,
        ...data[0]
      }

      // 如果任务完成或失败，停止轮询和假进度
      if (newStatus !== 'pending') {
        stopPolling()
        stopFakeProgress()
        if (oldStatus !== newStatus) {
          setTimeout(() => {
            getTaskDetail(taskDetail.value!._id)
          }, 1000)
        }
      }
    }
  },
  onError: (error) => {
    console.error('轮询失败:', error)
    vk.toast('获取任务状态失败')
  }
})

// 获取任务详情
const getTaskDetail = async (id: string) => {
  isLoading.value = true
  try {
    const res = await vk.callFunction({
      url: 'client/mx/task/kh/getTaskDetail',
      data: { id }
    })

    if (res.code === 0) {
      taskDetail.value = res.data
      // 如果是进行中的任务，启动轮询和假进度
      if (taskDetail.value?.status === 'pending') {
        startPolling([taskDetail.value._id])
        startFakeProgress()
      }
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    vk.toast('获取任务详情失败')
  } finally {
    isLoading.value = false
  }
}

// 页面加载时获取详情
onLoad((options: any) => {
  if (options.id) {
    getTaskDetail(options.id)
  }
})

// 添加加载状态
const isLoading = ref(true)

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '生成中',
    success: '生成成功',
    fail: '生成失败'
  } as const
  return statusMap[status as keyof typeof statusMap] || status
}

// 使用删除任务 hook
const { deleteTask } = useTaskDelete()

// 重新生成视频
const goToAiVideo = () => {
  // if (!taskDetail.value?.params?.prompt) {
  //   vk.toast('提示词不存在')
  //   return
  // }

  let params = {}
  if (isLumaTask.value) {
    params = {
      prompt: taskDetail.value?.params?.prompt,
      model: TaskType.LUMA,
      ratio: taskDetail.value?.params?.ratio,
      image: taskDetail.value?.params?.image,
      end_image: taskDetail.value?.params?.end_image
    }
  } else if (isZhipuTask.value) {
    params = {
      prompt: taskDetail.value?.params?.prompt,
      model: TaskType.ZHIPU,
      ratio: taskDetail.value?.params?.ratio,
      duration: taskDetail.value?.params?.duration,
      fps: taskDetail.value?.params?.fps,
      quality: taskDetail.value?.params?.quality,
      size: taskDetail.value?.params?.size,
      image: taskDetail.value?.params?.image
    }
  } else if (isWanxTask.value) {
    // 万相任务
    console.log('万相任务', taskDetail.value)
    params = {
      prompt: taskDetail.value?.params?.prompt,
      model: TaskType.WANX,
      ratio: taskDetail.value?.params?.ratio,
      duration: taskDetail.value?.params?.duration,
      image: taskDetail.value?.params?.image,
    }
    goToVideo('/pages/tools/wanx', params)
    return
  } else if (isKelingTask.value) {
    // 可灵任务
    console.log('可灵任务', taskDetail.value)
    params = {
      prompt: taskDetail.value?.params?.prompt,
      model: TaskType.KELING,
      ratio: taskDetail.value?.params?.ratio,
      duration: taskDetail.value?.params?.duration,
      image: taskDetail.value?.params?.image,
    }
    goToVideo('/pages/tools/keling-video', params)
    return
  } else if (isHeihe2Task.value) {
    // Heihe2任务
    console.log('Heihe2任务', taskDetail.value)
    params = {
      prompt: taskDetail.value?.params?.prompt,
      model: 'heihe2', // 使用ai-video页面期望的模型标识
      motion: taskDetail.value?.params?.motion,
      image: taskDetail.value?.params?.image, // 修正字段名
    }
    goToVideo('/pages/tools/ai-video', params)
    return
  } else {
    params = {
      prompt: taskDetail.value?.params?.prompt,
      ratio: taskDetail.value?.params?.ratio,
      duration: taskDetail.value?.params?.options?.seconds,
      image: taskDetail.value?.params?.image,
      model: TaskType.RUNWAY
    }
  }

  goToVideo('/pages/tools/ai-video', params)
}

/**
 * 跳转页面
 * @param pageUrl 页面路径
 * @param params 参数
 */
const goToVideo = (pageUrl: string, params: any) => {
  // 获取当前页面栈
  const pages = getCurrentPages()
  // 查找是否存在 ai-video 页面
  const aiVideoPage = pages.find(page => page.route === pageUrl)

  if (aiVideoPage) {
    // 如果存在，返回到该页面
    const delta = pages.length - pages.indexOf(aiVideoPage) - 1
    // 将参数通过事件通道传递
    uni.$emit('updateAiVideoParams', params)
    // 返回到已存在的页面
    uni.navigateBack({ delta })
  } else {
    // 如果不存在，创建新页面
    uni.navigateTo({
      url: `${pageUrl}?params=${encodeURIComponent(JSON.stringify(params))}`
    })
  }
}
// 添加复制提示词方法
const copyPrompt = (text: string) => {
  uni.setClipboardData({
    data: text,
    success: () => {
      vk.toast('复制成功')
    }
  })
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap = {
    pending: '#f1c68e',
    success: '#2ecc71',
    fail: '#e74c3c'
  } as const
  return colorMap[status as keyof typeof colorMap] || '#f1c68e'
}

// 获取状态背景色
const getStatusBgColor = (status: string) => {
  const bgColorMap = {
    pending: 'rgba(241, 198, 142, 0.1)',
    success: 'rgba(46, 204, 113, 0.1)',
    fail: 'rgba(231, 76, 60, 0.1)'
  } as const
  return bgColorMap[status as keyof typeof bgColorMap] || 'rgba(241, 198, 142, 0.1)'
}



// 添加预览图片方法
const previewImage = (url: string) => {
  uni.previewImage({
    urls: [url],
    current: url
  })
}

// 保存单个视频方法
const downloadSingleVideo = async (videoUrl: string, videoIndex?: number) => {
  if (!videoUrl) {
    vk.toast('视频地址不存在')
    return
  }

  try {
    // 获取用户授权
    const setting = await uni.getSetting()
    if (!setting.authSetting['scope.writePhotosAlbum']) {
      await uni.authorize({ scope: 'scope.writePhotosAlbum' })
    }

    const videoName = videoIndex !== undefined ? `视频 ${videoIndex + 1}` : '视频'
    vk.toast(`正在保存${videoName}...`, 'loading')

    // 下载视频文件
    const downloadRes = await uni.downloadFile({
      url: videoUrl
    })

    if (downloadRes.statusCode === 200) {
      // 保存视频到相册
      await uni.saveVideoToPhotosAlbum({
        filePath: downloadRes.tempFilePath
      })
      vk.toast(`${videoName}保存成功`, 'success')
    } else {
      throw new Error('下载失败')
    }
  } catch (error) {
    console.error('保存视频失败:', error)
    vk.toast('保存失败，请检查权限设置')
  }
}

// 扩展单个视频方法
const extendSingleVideo = async (videoIndex: number) => {
  if (!taskDetail.value) {
    vk.toast('任务信息不存在')
    return
  }

  try {
    // 确认对话框
    const confirmResult = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认扩展视频',
        content: `将基于视频 ${videoIndex + 1} 增加5秒时长，生成4个新视频。消耗20梦币，确定继续吗？`,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })

    if (!confirmResult) {
      return
    }

    const videoName = `视频 ${videoIndex + 1}`
    vk.toast(`正在扩展${videoName}...`, 'loading')

    // 调用扩展API
    const res = await vk.callFunction({
      url: 'client/mx/kh.api.createHeihe2VideoTask',
      data: {
        prompt: taskDetail.value.params?.prompt || '',
        motion: taskDetail.value.params?.motion || 'low',
        image: taskDetail.value.params?.image || '',
        action: 'extend',
        index: videoIndex,
        taskId: taskDetail.value.task_id || taskDetail.value._id,
        mx_coin: 20
      }
    })

    if (res.code === 0) {
      vk.toast(`${videoName}扩展任务创建成功`, 'success')
      // 跳转到新任务详情页
      uni.navigateTo({
        url: `/pages/tasks/ai-video/detail?id=${res.data._id}`
      })
    } else {
      throw new Error(res.msg || '扩展失败')
    }
  } catch (error: any) {
    console.error('扩展视频失败:', error)
    vk.toast(error.message || '扩展失败，请重试')
  }
}



// 添加VIP状态缓存
const isVip = ref(false)

// 全屏播放相关状态
const isFullscreen = ref(false)
const fullscreenVideoUrl = ref('')
const fullscreenVideoIndex = ref<number | null>(null)

// 导航栏高度处理（保留用于其他功能）
const navbarHeight = ref(88) // 默认高度，单位rpx

// 处理视频缩略图加载完成（简化版，不再需要尺寸计算）
const handleVideoThumbnailLoad = (_: Event, index: number) => {
  console.log(`视频缩略图 ${index + 1} 加载完成`)
}

// 点击视频直接进入全屏播放
const playVideo = (index: number) => {
  if (isHeihe2Task.value && Array.isArray(taskDetail.value?.result)) {
    const video = (taskDetail.value.result as Heihe2VideoItem[])[index]
    if (video?.uploadedUrl) {
      enterFullscreen(video.uploadedUrl, index)
    }
  } else if (taskDetail.value?.result) {
    enterFullscreen(taskDetail.value.result as string, 0)
  }
}

// 进入全屏播放
const enterFullscreen = (videoUrl: string, index?: number) => {
  isFullscreen.value = true
  fullscreenVideoUrl.value = videoUrl
  fullscreenVideoIndex.value = index ?? null

  // 阻止页面滚动
  // #ifdef H5
  document.body.style.overflow = 'hidden'
  // #endif
}

// 退出全屏播放
const exitFullscreen = () => {
  isFullscreen.value = false
  fullscreenVideoUrl.value = ''
  fullscreenVideoIndex.value = null

  // 恢复页面滚动
  // #ifdef H5
  document.body.style.overflow = 'auto'
  // #endif
}



// 导航栏初始化完成处理
const onNavbarInit = (info: any) => {
  console.log('导航栏初始化完成:', info)
  // 将px转换为rpx (1px = 2rpx)
  navbarHeight.value = info.height * 2
  console.log('导航栏实际高度:', navbarHeight.value, 'rpx')
}

// 注意：移除了动态样式计算，使用固定的1:1比例确保网格布局稳定

onMounted(() => {
  // 组件挂载时检查一次VIP状态
  isVip.value = vk.myfn.checkVip()

  // 小程序环境不支持 document 对象，移除键盘事件监听
  // #ifdef H5
  document.addEventListener('keydown', handleKeyDown)
  // #endif
})

// 在组件卸载时停止轮询和假进度
onUnmounted(() => {
  stopPolling()
  stopFakeProgress()

  // 确保退出全屏状态
  if (isFullscreen.value) {
    exitFullscreen()
  }

  // 小程序环境不支持 document 对象，移除键盘事件监听
  // #ifdef H5
  document.removeEventListener('keydown', handleKeyDown)
  // #endif
})

// 处理键盘事件（简化版）
const handleKeyDown = (event: KeyboardEvent) => {
  // ESC键退出全屏
  if (event.key === 'Escape' && isFullscreen.value) {
    exitFullscreen()
    return
  }

  // 可以在这里添加其他键盘快捷键
  console.log('键盘事件:', event.key)
}

</script>

<template>
  <TnNavbar fixed :bottom-shadow="false" bg-color="transparent" title-color="#fff" :placeholder="true" @init-finish="onNavbarInit">
    任务详情
  </TnNavbar>

  <view class="page-container">
    <!-- 主要内容区域 -->
    <view class="content-wrapper">
      <template v-if="taskDetail">
        <!-- 视频预览区域 -->
        <view class="preview-section" :class="{ 'heihe2-layout': isHeihe2Task }">
          <template v-if="taskDetail.status === 'success'">
            <!-- Midjourney多视频显示 -->
            <template v-if="isHeihe2Task">
              <view class="heihe2-videos">
                <view class="videos-grid">
                  <view
                    v-for="(video, index) in (Array.isArray(taskDetail.result) ? taskDetail.result as Heihe2VideoItem[] : [])"
                    :key="index"
                    class="video-item"
                  >
                    <!-- 视频缩略图，点击直接全屏播放 -->
                    <video
                      :src="video.uploadedUrl"
                      class="heihe2-reference-video"
                      :poster="taskDetail.params?.image"
                      preload="metadata"
                      muted
                      playsinline
                      @loadeddata="(e) => handleVideoThumbnailLoad(e, index)"
                    ></video>
                    <!-- 播放按钮覆盖层 -->
                    <view class="play-overlay" @click="playVideo(index)">
                      <view class="play-button">
                        <TnIcon name="play-fill" size="48" color="#fff" />
                      </view>
                      <view class="video-index-badge">{{ index + 1 }}</view>
                      <view class="video-title">视频 {{ index + 1 }}</view>
                      <view class="fullscreen-hint">点击全屏播放</view>
                    </view>
                  </view>
                </view>
              </view>
            </template>
            <!-- 其他任务的单视频显示 -->
            <template v-else>
              <view class="poster-container" @tap="() => playVideo(0)">
                <image v-if="taskDetail.params.my_poster" :src="taskDetail.params.my_poster" mode="aspectFit"
                  class="poster-image" />
                <view class="play-button">
                  <TnIcon name="play-fill" size="60" color="#fff" />
                </view>
                <view class="fullscreen-hint">点击全屏播放</view>
              </view>
            </template>
          </template>
          <template v-else>
            <view class="generating-box">
              <template v-if="taskDetail.status === 'fail'">
                <!-- 失败状态显示 -->
                <view class="failure-content">
                  <TnIcon name="warning" size="50" color="#e74c3c" />
                  <text class="failure-text">{{ taskDetail.fail_reason || '生成失败' }}</text>
                </view>
              </template>
              <template v-else>
                <!-- 圆形进度显示 -->
                <view class="progress-circle">
                  <view class="circle-wrap">
                    <view class="circle-bg"></view>
                    <view class="circle-content">
                      <text class="progress-text">{{ fakeProgress }}%</text>
                    </view>
                  </view>
                </view>
                <!-- 添加普通用户提示 -->
                <view class="queue-tip" v-if="taskDetail.status === 'pending' && !isVip">
                  <TnIcon name="info" size="28" color="rgba(241, 198, 142, 0.8)" />
                  <text>当前为普通通道，可能存在排队现象，VIP免排队</text>
                </view>
                <view class="queue-tip" v-if="taskDetail.status === 'pending' && isVip">
                  <TnIcon name="info" size="28" color="rgba(241, 198, 142, 0.8)" />
                  <text>当前为VIP极速通道</text>
                </view>
              </template>
            </view>
          </template>
        </view>

        <!-- 下载功能区域 -->
        <view class="download-section" v-if="taskDetail.status === 'success'">
          <text class="section-title">下载视频</text>

          <!-- 多视频下载选项 -->
          <template v-if="isHeihe2Task && Array.isArray(taskDetail.result)">
            <view class="download-grid">
              <view
                v-for="(video, index) in (taskDetail.result as Heihe2VideoItem[])"
                :key="index"
                class="download-item"
                @click="downloadSingleVideo(video.uploadedUrl, index)"
              >
                <view class="download-number">{{ index + 1 }}</view>
              </view>
            </view>
          </template>

          <!-- 单视频下载选项 -->
          <template v-else>
            <view class="download-grid single-video">
              <view class="download-item" @click="downloadSingleVideo(taskDetail.result as string)">
                <TnIcon name="download" size="24" color="#f1c68e" />
                <text class="download-text">下载视频</text>
              </view>
            </view>
          </template>
        </view>

        <!-- 扩展功能区域 -->
        <view class="extend-section" v-if="taskDetail.status === 'success' && isHeihe2Task && Array.isArray(taskDetail.result)">
          <text class="section-title">扩展视频</text>
          <view class="extend-description">
            <text class="extend-subtitle">在原视频基础上增加5秒时长，生成4个新视频</text>
            <text class="extend-cost">每次扩展消耗20梦币</text>
          </view>

          <view class="extend-grid">
            <view
              v-for="(_, index) in (taskDetail.result as Heihe2VideoItem[])"
              :key="index"
              class="extend-item"
              @click="extendSingleVideo(index)"
            >
              <TnIcon name="expand" size="20" color="#f1c68e" />
              <view class="extend-number">{{ index + 1 }}</view>
            </view>
          </view>
        </view>

        <!-- 信息卡片区域 -->
        <view class="info-card">
          <!-- 状态栏 -->
          <view class="status-bar">
            <view class="status-tag" :style="{
              color: getStatusColor(taskDetail.status),
              background: getStatusBgColor(taskDetail.status)
            }">
              {{ getStatusText(taskDetail.status) }}
            </view>
            <text class="time-text">{{ taskDetail._add_time_str }}</text>
          </view>

          <!-- 提示词 -->
          <view class="prompt-section">
            <text class="section-title">提示词</text>
            <!-- 中文提示词 -->
            <view class="prompt-block" @click="copyPrompt(taskDetail.params.prompt)">
              <view class="prompt-header">
                <view class="prompt-tag">中文</view>
                <view class="copy-hint">
                  <TnIcon name="copy" size="28" color="rgba(255,255,255,0.3)" />
                  <text>点击复制</text>
                </view>
              </view>
              <view class="prompt-content">{{ taskDetail.params.prompt }}</view>
            </view>

            <!-- 英文提示词 -->
            <view class="prompt-block" @click="copyPrompt(taskDetail.params.prompt_en || '')"
              v-if="taskDetail.params.prompt_en">
              <view class="prompt-header">
                <view class="prompt-tag">英文</view>
                <view class="copy-hint">
                  <TnIcon name="copy" size="28" color="rgba(255,255,255,0.3)" />
                  <text>点击复制</text>
                </view>
              </view>
              <view class="prompt-content en">{{ taskDetail?.params?.prompt_en }}</view>
            </view>
          </view>

          <!-- 参数信息 -->
          <view class="params-section">
            <text class="section-title">生成参数</text>
            <view class="params-grid">
              <view class="param-item" v-for="(param, index) in getParamsConfig" :key="index">
                <text class="label">{{ param.label }}</text>
                <text class="value">{{ param.value }}</text>
              </view>
            </view>


            <template v-if="taskDetail?.params?.image">
              <view class="reference-section">
                <text class="sub-title">参考图片</text>
                <view class="reference-image-wrapper">
                  <image :src="taskDetail.params.image" mode="aspectFill" @click="previewImage(taskDetail.params.image)"
                    class="reference-image" />
                  <view class="image-overlay" @click="previewImage(taskDetail.params.image)">
                    <TnIcon name="eye" size="40" color="#fff" />
                  </view>
                </view>

              </view>
              <view class="reference-section" v-if="taskDetail.params.end_image">
                <text class="sub-title">尾帧图片</text>
                <view class="reference-image-wrapper">
                  <image :src="taskDetail.params.end_image" mode="aspectFill"
                    @click="previewImage(taskDetail.params.end_image)" class="reference-image" />
                  <view class="image-overlay" @click="previewImage(taskDetail.params.end_image)">
                    <TnIcon name="eye" size="40" color="#fff" />
                  </view>
                </view>
              </view>
            </template>
          </view>

          <!-- 错误信息 -->
          <view class="error-section" v-if="taskDetail.status === 'fail'">
            <view class="error-box">
              <TnIcon name="warning" size="36" color="#e74c3c" />
              <text>生成失败，请重试</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 加载状态 -->
      <template v-else>
        <view class="loading-state">
          <TnIcon name="refresh" size="50" color="#f1c68e" />
          <text>加载中...</text>
        </view>
      </template>
    </view>

    <!-- 底部按钮区域 -->
    <view class="bottom-actions" v-if="taskDetail?.status !== 'pending'">
      <view class="action-wrapper">
        <!-- 主要按钮 -->
        <view class="primary-actions">
          <view class="action-btn primary" @click="goToAiVideo">
            <view class="btn-content">
              <TnIcon name="creative" size="32" color="#634738" />
              <text>重新生成</text>
            </view>
          </view>
        </view>
        <!-- 删除按钮 -->
        <view class="delete-btn" @click="() => taskDetail?._id && deleteTask(taskDetail._id)">
          <TnIcon name="delete" size="36" color="rgba(255,255,255,0.8)" />
        </view>
      </view>
    </view>
  </view>

  <!-- 全屏视频遮罩层 -->
  <view v-if="isFullscreen" class="fullscreen-overlay" @click="exitFullscreen">
    <view class="fullscreen-container" @click.stop>
      <!-- 关闭按钮 -->
      <view class="fullscreen-close-btn" @click="exitFullscreen">
        <TnIcon name="close" size="32" color="#fff" />
      </view>

      <!-- 全屏视频播放器 -->
      <video
        :src="fullscreenVideoUrl"
        class="fullscreen-video"
        controls
        show-center-play-btn
        object-fit="contain"
        type="video/mp4"
        autoplay
        loop
        playsinline
        @error="exitFullscreen"
      ></video>

      <!-- 视频信息 -->
      <view class="fullscreen-info" v-if="fullscreenVideoIndex !== null">
        <text class="video-title">
          {{ isHeihe2Task ? `视频 ${fullscreenVideoIndex + 1}` : '视频播放' }}
        </text>
      </view>
    </view>
  </view>

</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1A1F2B 0%, #2A2F3B 100%);
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  padding: 30rpx;
  padding-bottom: calc(180rpx + env(safe-area-inset-bottom));
  overflow-y: auto;

  // 下载功能区域
  .download-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 24rpx;
    padding: 30rpx;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    margin: 30rpx 0;

    .section-title {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;

      &::before {
        content: '';
        width: 4rpx;
        height: 24rpx;
        background: #f1c68e;
        border-radius: 2rpx;
      }
    }

    .download-grid {
      display: flex;
      gap: 12rpx;

      &.single-video {
        justify-content: flex-start;

        .download-item {
          width: 200rpx;
          flex-direction: row;
          gap: 8rpx;
        }
      }

      .download-item {
        flex: 1;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(255, 255, 255, 0.05);
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        cursor: pointer;

        &:active {
          background: rgba(255, 255, 255, 0.08);
          transform: scale(0.98);
        }

        .download-number {
          font-size: 24rpx;
          color: #f1c68e;
          font-weight: 600;
        }

        .download-text {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.8);
          margin-top: 4rpx;
        }

        // 悬停效果
        &:hover {
          background: rgba(241, 198, 142, 0.1);
          border-color: rgba(241, 198, 142, 0.2);
        }
      }
    }
  }

  // 扩展功能区域
  .extend-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 24rpx;
    padding: 30rpx;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    margin: 30rpx 0;

    .section-title {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 12rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;

      &::before {
        content: '';
        width: 4rpx;
        height: 24rpx;
        background: #f1c68e;
        border-radius: 2rpx;
      }
    }

    .extend-description {
      margin-bottom: 20rpx;

      .extend-subtitle {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.7);
        display: block;
        margin-bottom: 6rpx;
        line-height: 1.4;
      }

      .extend-cost {
        font-size: 22rpx;
        color: rgba(241, 198, 142, 0.8);
        display: block;
      }
    }

    .extend-grid {
      display: flex;
      gap: 12rpx;

      .extend-item {
        flex: 1;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(255, 255, 255, 0.05);
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        cursor: pointer;
        gap: 4rpx;

        &:active {
          background: rgba(255, 255, 255, 0.08);
          transform: scale(0.98);
        }

        .extend-number {
          font-size: 20rpx;
          color: #f1c68e;
          font-weight: 600;
        }

        // 悬停效果
        &:hover {
          background: rgba(241, 198, 142, 0.1);
          border-color: rgba(241, 198, 142, 0.2);
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  background: linear-gradient(to top, rgba(26, 31, 43, 0.98), rgba(26, 31, 43, 0.95));
  backdrop-filter: blur(20px);
  z-index: 100;

  .action-wrapper {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .primary-actions {
    flex: 1;
    display: flex;
    gap: 12rpx;

    .action-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.25s ease;

      .btn-content {
        display: flex;
        align-items: center;
        gap: 8rpx;
      }

      text {
        font-size: 24rpx;
        font-weight: 500;
      }

      &.primary {
        background: linear-gradient(to right, #E3C088, #F7DEB3);

        text {
          color: #634738;
        }

        &:active {
          transform: scale(0.98);
          opacity: 0.9;
        }
      }

      &.download {
        background: rgba(241, 198, 142, 0.1);
        border: 1px solid rgba(241, 198, 142, 0.2);

        text {
          color: #f1c68e;
        }

        &:active {
          transform: scale(0.98);
          background: rgba(241, 198, 142, 0.15);
        }
      }
    }
  }

  .delete-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.25s ease;

    &:active {
      transform: scale(0.95);
      background: rgba(255, 255, 255, 0.08);
    }
  }
}

.preview-section {
  margin: 20rpx 0 40rpx;
  position: relative;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  background: #000;
  aspect-ratio: 16/9; // 默认16:9比例

  // heihe2任务使用正方形比例适合2x2网格
  &.heihe2-layout {
    aspect-ratio: 1/1;
  }

  .video-player {
    width: 100%;
    height: 100%;
    display: block;
  }

  .poster-container {
    width: 100%;
    height: 100%;
    position: relative;

    .poster-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120rpx;
      height: 120rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:active {
        transform: translate(-50%, -50%) scale(0.95);
        background: rgba(0, 0, 0, 0.6);
      }
    }
  }

  .generating-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(241, 198, 142, 0.05);
    padding: 40rpx;
    position: relative;
    overflow: hidden;
    border-radius: 24rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 2px solid rgba(241, 198, 142, 0.3);
      border-radius: 24rpx;
      animation: breathe 2s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at center,
          rgba(241, 198, 142, 0.1) 0%,
          rgba(241, 198, 142, 0.05) 50%,
          transparent 100%);
      animation: pulse 3s ease-in-out infinite;
      z-index: -1;
    }

    .progress-circle {
      margin: 40rpx 0;

      .circle-wrap {
        position: relative;
        width: 180rpx;
        height: 180rpx;
        margin: 0 auto;
      }

      .circle-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
      }

      .circle-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;

        .progress-text {
          font-size: 48rpx;
          color: #f1c68e;
          font-weight: bold;
        }
      }
    }

    .queue-tip {
      position: absolute;
      bottom: 30rpx;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      gap: 8rpx;
      background: rgba(0, 0, 0, 0.3);
      padding: 12rpx 24rpx;
      border-radius: 100rpx;
      backdrop-filter: blur(10px);
      animation: tipBreathe 2s ease-in-out infinite;
      white-space: nowrap;

      text {
        font-size: 24rpx;
        color: rgba(241, 198, 142, 0.8);
      }
    }

    .failure-content {
      text-align: center;
      padding: 40rpx;

      .failure-text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #e74c3c;
        line-height: 1.6;
      }
    }
  }
}

.info-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 24rpx;
  padding: 30rpx;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  margin-bottom: 200rpx;

  // 状态栏样式
  .status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;

    .status-tag {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 24rpx;
      border-radius: 100rpx;
      font-size: 24rpx;
      font-weight: 500;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .time-text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.4);
    }
  }

  // 提示词区域
  .prompt-section {
    margin-bottom: 40rpx;

    .section-title {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;

      &::before {
        content: '';
        width: 4rpx;
        height: 24rpx;
        background: #f1c68e;
        border-radius: 2rpx;
      }
    }

    .prompt-block {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 16rpx;
      border: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.05);
      }

      .prompt-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;

        .prompt-tag {
          font-size: 22rpx;
          color: #f1c68e;
          background: rgba(241, 198, 142, 0.1);
          padding: 4rpx 16rpx;
          border-radius: 100rpx;
        }

        .copy-hint {
          display: flex;
          align-items: center;
          gap: 6rpx;
          opacity: 0.4;

          text {
            font-size: 22rpx;
            color: #fff;
          }
        }
      }

      .prompt-content {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;

        &.en {
          color: rgba(255, 255, 255, 0.7);
          // font-style: italic;
        }
      }
    }
  }

  // 参数信息区域
  .params-section {
    .section-title {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;

      &::before {
        content: '';
        width: 4rpx;
        height: 24rpx;
        background: #f1c68e;
        border-radius: 2rpx;
      }
    }

    .params-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16rpx;

      .param-item {
        background: rgba(255, 255, 255, 0.03);
        padding: 20rpx;
        border-radius: 16rpx;
        border: 1px solid rgba(255, 255, 255, 0.05);

        .label {
          font-size: 22rpx;
          color: rgba(255, 255, 255, 0.4);
          margin-bottom: 8rpx;
        }

        .value {
          margin-left: 8rpx;
          font-size: 22rpx;
          color: #fff;
          font-weight: 500;
        }
      }
    }

    // 参考图显示
    .reference-section {
      margin-top: 30rpx;

      .sub-title {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
        gap: 8rpx;

        &::before {
          content: '';
          width: 4rpx;
          height: 24rpx;
          background: #f1c68e;
          border-radius: 2rpx;
        }
      }

      .reference-image-wrapper {
        position: relative;
        width: 240rpx;
        height: 240rpx;
        border-radius: 16rpx;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(241, 198, 142, 0.1);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        .reference-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          backdrop-filter: blur(4px);
        }

        &:active {
          .reference-image {
            transform: scale(1.05);
          }

          .image-overlay {
            opacity: 1;
          }
        }
      }
    }
  }

  // 错误信息区域
  .error-section {
    margin-top: 30rpx;

    .error-box {
      display: flex;
      align-items: center;
      gap: 12rpx;
      padding: 20rpx;
      background: rgba(231, 76, 60, 0.05);
      border: 1px solid rgba(231, 76, 60, 0.1);
      border-radius: 16rpx;

      text {
        font-size: 26rpx;
        color: #e74c3c;
        flex: 1;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 添加封面图相关样式
.poster-container {
  position: relative;
  width: 100%;
  height: 480rpx;
  border-radius: 32rpx;
  overflow: hidden;
  background: #000;

  .poster-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2rpx solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:active {
      transform: translate(-50%, -50%) scale(0.95);
      background: rgba(0, 0, 0, 0.6);
    }
  }

  .fullscreen-hint {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    background: rgba(241, 198, 142, 0.8);
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    color: #2a2f3b;
    font-weight: 500;
    backdrop-filter: blur(10px);
    opacity: 0.8;
  }
}

// 确保视频播放器样式保持不变
.video-player {
  width: 100%;
  height: 480rpx;
  background: #000;
  display: block;
  border-radius: 32rpx;
}

// Midjourney多视频样式
.heihe2-videos {
  width: 100%;
  height: 100%;
  padding: 20rpx;

  .videos-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 16rpx;
    width: 100%;
    height: 100%;

    .video-item {
      position: relative;
      border-radius: 12rpx;
      overflow: hidden;
      background: rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
      // 强制固定长宽比，确保不超出网格
      aspect-ratio: 1/1;
      // 确保内容不会超出容器
      width: 100%;
      height: 100%;
      min-height: 0;
      min-width: 0;

      &:active {
        transform: scale(0.98);
      }

      // 参考图片样式
      .heihe2-reference-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12rpx;
        background: #000;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }

      // 视频缩略图样式
      .heihe2-reference-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12rpx;
        background: #000;
        // 禁用视频控件和交互，只作为缩略图显示
        pointer-events: none;
        // 确保视频不会自动播放
        &::-webkit-media-controls {
          display: none !important;
        }
      }

      // 播放中的视频样式
      .heihe2-playing-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 12rpx;
        background: #000;
        z-index: 10; // 确保播放器在覆盖层之上
      }



      // 播放覆盖层
      .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 12rpx;
        opacity: 1; // 始终显示，让用户知道这是可点击的视频
        transition: all 0.3s ease;
        cursor: pointer;
        // 确保覆盖层可以接收点击事件
        pointer-events: auto;

        .play-button {
          width: 96rpx;
          height: 96rpx;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
          border: 2rpx solid rgba(255, 255, 255, 0.3);
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
            background: rgba(0, 0, 0, 0.8);
          }
        }

        .video-index-badge {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          width: 48rpx;
          height: 48rpx;
          background: linear-gradient(135deg, #f1c68e 0%, #d4a574 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          color: #2a2f3b;
          font-weight: 700;
          box-shadow: 0 4rpx 12rpx rgba(241, 198, 142, 0.4);
        }

        .video-title {
          position: absolute;
          bottom: 16rpx;
          left: 16rpx;
          background: rgba(0, 0, 0, 0.7);
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 22rpx;
          color: #fff;
          backdrop-filter: blur(10px);
          border: 1rpx solid rgba(255, 255, 255, 0.1);
        }

        .fullscreen-hint {
          position: absolute;
          bottom: 16rpx;
          right: 16rpx;
          background: rgba(241, 198, 142, 0.8);
          padding: 6rpx 12rpx;
          border-radius: 16rpx;
          font-size: 20rpx;
          color: #2a2f3b;
          font-weight: 500;
          backdrop-filter: blur(10px);
          opacity: 0.8;
        }
      }



      // 鼠标悬停时增强播放覆盖层效果
      &:hover .play-overlay {
        background: rgba(0, 0, 0, 0.5);

        .play-button {
          transform: scale(1.1);
          background: rgba(0, 0, 0, 0.8);
        }
      }


    }
  }
}

// 呼吸动画
@keyframes breathe {

  0%,
  100% {
    box-shadow: 0 0 20rpx rgba(241, 198, 142, 0.2),
      inset 0 0 20rpx rgba(241, 198, 142, 0.1);
    border-color: rgba(241, 198, 142, 0.4);
  }

  50% {
    box-shadow: 0 0 40rpx rgba(241, 198, 142, 0.4),
      inset 0 0 40rpx rgba(241, 198, 142, 0.2);
    border-color: rgba(241, 198, 142, 0.6);
  }
}

// 脉冲动画
@keyframes pulse {

  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

// 示文字动画
@keyframes tipBreathe {

  0%,
  100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }

  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.02);
  }
}

// 全屏遮罩层样式
.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);

  .fullscreen-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 40rpx;

    .fullscreen-close-btn {
      position: absolute;
      top: 60rpx;
      right: 60rpx;
      width: 80rpx;
      height: 80rpx;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      backdrop-filter: blur(10px);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background: rgba(0, 0, 0, 0.8);
      }

      &:hover {
        background: rgba(0, 0, 0, 0.7);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    .fullscreen-video {
      width: 100%;
      height: 80%;
      max-width: 100vw;
      max-height: 80vh;
      background: #000;
      border-radius: 16rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
    }

    .fullscreen-info {
      position: absolute;
      bottom: 80rpx;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.7);
      padding: 16rpx 32rpx;
      border-radius: 100rpx;
      backdrop-filter: blur(10px);
      border: 1rpx solid rgba(255, 255, 255, 0.1);

      .video-title {
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
      }
    }
  }
}

// 小程序环境适配
/* #ifdef MP */
.fullscreen-overlay {
  .fullscreen-container {
    .fullscreen-close-btn {
      top: calc(60rpx + env(safe-area-inset-top));
    }
  }
}
/* #endif */


</style>